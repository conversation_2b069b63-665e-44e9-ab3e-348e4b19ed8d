package com.dell.it.hip.core;

import com.dell.it.hip.config.FlowSteps.DocTypeProcessorStepConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class JsonDeserializationTest {

    @Test
    public void testDocTypeProcessorStepConfigDotNotationDeserialization() throws Exception {
        // JSON string with dot notation format that the custom deserializer handles
        String json = "{\n"
                + "  \"supportedDocTypesPerFormat.JSON[0]\": \"testdoc:1\",\n"
                + "  \"supportedDocTypesPerFormat.JSON[1]\": \"testdoce1:1\",\n"
                + "  \"genericDocType\": \"GENERIC\",\n"
                + "  \"allowGenericDocType\": true,\n"
                + "  \"terminateOnUnknownFormat\": false\n"
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();

        // Test deserialization
        DocTypeProcessorStepConfig config = objectMapper.readValue(json, DocTypeProcessorStepConfig.class);

        // Verify the deserialization worked correctly
        assertNotNull(config);
        assertNotNull(config.getSupportedDocTypesPerFormat());
        assertTrue(config.getSupportedDocTypesPerFormat().containsKey("JSON"));
        assertEquals(2, config.getSupportedDocTypesPerFormat().get("JSON").size());
        assertEquals("testdoc:1", config.getSupportedDocTypesPerFormat().get("JSON").get(0));
        assertEquals("testdoce1:1", config.getSupportedDocTypesPerFormat().get("JSON").get(1));
        assertEquals("GENERIC", config.getGenericDocType());
        assertTrue(config.isAllowGenericDocType());
        assertFalse(config.isTerminateOnUnknownFormat());

        System.out.println("Successfully deserialized config: " + config);
        System.out.println("Supported doc types for JSON: " + config.getSupportedDocTypesPerFormat().get("JSON"));
    }

    @Test
    public void testStandardJsonFormatDeserialization() throws Exception {
        // Test that standard JSON format also works (without custom deserializer logic)
        String standardJson = "{\n"
                + "  \"genericDocType\": \"GENERIC\",\n"
                + "  \"allowGenericDocType\": true,\n"
                + "  \"terminateOnUnknownFormat\": false\n"
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();

        // This should work fine - testing basic properties without supportedDocTypesPerFormat
        DocTypeProcessorStepConfig config = objectMapper.readValue(standardJson, DocTypeProcessorStepConfig.class);

        assertNotNull(config);
        assertEquals("GENERIC", config.getGenericDocType());
        assertTrue(config.isAllowGenericDocType());
        assertFalse(config.isTerminateOnUnknownFormat());
        // supportedDocTypesPerFormat should be empty/null since not provided
        assertTrue(config.getSupportedDocTypesPerFormat() == null || config.getSupportedDocTypesPerFormat().isEmpty());

        System.out.println("Successfully deserialized standard JSON format!");
    }
}
