package com.dell.it.hip.exception;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class IntegrationRegistrationExceptionTest {

    @Test
    void testBasicConstructor() {
        String message = "Registration failed";
        IntegrationRegistrationException exception = new IntegrationRegistrationException(message);
        
        assertEquals(message, exception.getMessage());
        assertNull(exception.getIntegrationName());
        assertNull(exception.getVersion());
    }

    @Test
    void testConstructorWithCause() {
        String message = "Registration failed";
        RuntimeException cause = new RuntimeException("Root cause");
        IntegrationRegistrationException exception = new IntegrationRegistrationException(message, cause);
        
        assertEquals(message, exception.getMessage());
        assertEquals(cause, exception.getCause());
        assertNull(exception.getIntegrationName());
        assertNull(exception.getVersion());
    }

    @Test
    void testConstructorWithIntegrationDetails() {
        String integrationName = "test-integration";
        String version = "1.0";
        String message = "Configuration error";
        
        IntegrationRegistrationException exception = new IntegrationRegistrationException(
            integrationName, version, message);
        
        assertTrue(exception.getMessage().contains(integrationName));
        assertTrue(exception.getMessage().contains(version));
        assertTrue(exception.getMessage().contains(message));
        assertEquals(integrationName, exception.getIntegrationName());
        assertEquals(version, exception.getVersion());
    }

    @Test
    void testConstructorWithIntegrationDetailsAndCause() {
        String integrationName = "test-integration";
        String version = "1.0";
        String message = "Configuration error";
        RuntimeException cause = new RuntimeException("Root cause");
        
        IntegrationRegistrationException exception = new IntegrationRegistrationException(
            integrationName, version, message, cause);
        
        assertTrue(exception.getMessage().contains(integrationName));
        assertTrue(exception.getMessage().contains(version));
        assertTrue(exception.getMessage().contains(message));
        assertEquals(integrationName, exception.getIntegrationName());
        assertEquals(version, exception.getVersion());
        assertEquals(cause, exception.getCause());
    }

    @Test
    void testConstructorWithCauseOnly() {
        String integrationName = "test-integration";
        String version = "1.0";
        RuntimeException cause = new RuntimeException("Root cause");
        
        IntegrationRegistrationException exception = new IntegrationRegistrationException(
            integrationName, version, cause);
        
        assertTrue(exception.getMessage().contains(integrationName));
        assertTrue(exception.getMessage().contains(version));
        assertTrue(exception.getMessage().contains("Root cause"));
        assertEquals(integrationName, exception.getIntegrationName());
        assertEquals(version, exception.getVersion());
        assertEquals(cause, exception.getCause());
    }
}
