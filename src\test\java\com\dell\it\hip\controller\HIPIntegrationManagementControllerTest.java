package com.dell.it.hip.controller;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.core.HIPIntegrationOrchestrationService;
import com.dell.it.hip.core.ServiceManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for HIPIntegrationManagementController definition retrieval methods.
 * Tests both single definition retrieval and multi-version definition retrieval.
 */
@ExtendWith(MockitoExtension.class)
public class HIPIntegrationManagementControllerTest {

    @Mock
    private HIPIntegrationOrchestrationService orchestrationService;

    @Mock
    private ServiceManager serviceManager;

    @InjectMocks
    private HIPIntegrationManagementController controller;

    private HIPIntegrationDefinition testDefinitionV1;
    private HIPIntegrationDefinition testDefinitionV2;
    private HIPIntegrationDefinition testDefinitionV3;

    @BeforeEach
    void setUp() {
        // Create test definitions for different versions of the same integration
        testDefinitionV1 = createTestDefinition("test-integration", "1.0");
        testDefinitionV2 = createTestDefinition("test-integration", "2.0");
        testDefinitionV3 = createTestDefinition("test-integration", "3.0");
    }

    // === Tests for existing getDefinition method ===

    @Test
    void testGetDefinition_Success() {
        // Arrange
        when(serviceManager.getIntegrationDefinition("test-integration", "1.0"))
                .thenReturn(testDefinitionV1);

        // Act
        ResponseEntity<?> response = controller.getDefinition("test-integration", "1.0");

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertTrue(response.getBody() instanceof HIPIntegrationDefinition);
        HIPIntegrationDefinition returnedDef = (HIPIntegrationDefinition) response.getBody();
        assertEquals("test-integration", returnedDef.getHipIntegrationName());
        assertEquals("1.0", returnedDef.getVersion());
        
        verify(serviceManager).getIntegrationDefinition("test-integration", "1.0");
    }

    @Test
    void testGetDefinition_NotFound() {
        // Arrange
        when(serviceManager.getIntegrationDefinition("non-existent", "1.0"))
                .thenReturn(null);

        // Act
        ResponseEntity<?> response = controller.getDefinition("non-existent", "1.0");

        // Assert
        assertEquals(404, response.getStatusCode().value());
        verify(serviceManager).getIntegrationDefinition("non-existent", "1.0");
    }

    // === Tests for new getDefinitionByName method ===

    @Test
    void testGetDefinitionByName_Success_MultipleVersions() {
        // Arrange
        List<HIPIntegrationDefinition> expectedDefinitions = Arrays.asList(
                testDefinitionV1, testDefinitionV2, testDefinitionV3);
        when(serviceManager.getDefinitionsByName("test-integration"))
                .thenReturn(expectedDefinitions);

        // Act
        ResponseEntity<?> response = controller.getDefinitionByName("test-integration");

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertTrue(response.getBody() instanceof List);
        
        @SuppressWarnings("unchecked")
        List<HIPIntegrationDefinition> returnedDefinitions = (List<HIPIntegrationDefinition>) response.getBody();
        assertEquals(3, returnedDefinitions.size());
        
        // Verify all definitions have the same integration name
        returnedDefinitions.forEach(def -> 
                assertEquals("test-integration", def.getHipIntegrationName()));
        
        // Verify different versions are present
        assertTrue(returnedDefinitions.stream().anyMatch(def -> "1.0".equals(def.getVersion())));
        assertTrue(returnedDefinitions.stream().anyMatch(def -> "2.0".equals(def.getVersion())));
        assertTrue(returnedDefinitions.stream().anyMatch(def -> "3.0".equals(def.getVersion())));
        
        verify(serviceManager).getDefinitionsByName("test-integration");
    }

    @Test
    void testGetDefinitionByName_Success_SingleVersion() {
        // Arrange
        List<HIPIntegrationDefinition> expectedDefinitions = Collections.singletonList(testDefinitionV1);
        when(serviceManager.getDefinitionsByName("single-version-integration"))
                .thenReturn(expectedDefinitions);

        // Act
        ResponseEntity<?> response = controller.getDefinitionByName("single-version-integration");

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertTrue(response.getBody() instanceof List);
        
        @SuppressWarnings("unchecked")
        List<HIPIntegrationDefinition> returnedDefinitions = (List<HIPIntegrationDefinition>) response.getBody();
        assertEquals(1, returnedDefinitions.size());
        assertEquals("test-integration", returnedDefinitions.get(0).getHipIntegrationName());
        assertEquals("1.0", returnedDefinitions.get(0).getVersion());
        
        verify(serviceManager).getDefinitionsByName("single-version-integration");
    }

    @Test
    void testGetDefinitionByName_NotFound_EmptyList() {
        // Arrange
        when(serviceManager.getDefinitionsByName("non-existent-integration"))
                .thenReturn(Collections.emptyList());

        // Act
        ResponseEntity<?> response = controller.getDefinitionByName("non-existent-integration");

        // Assert
        assertEquals(404, response.getStatusCode().value());
        verify(serviceManager).getDefinitionsByName("non-existent-integration");
    }

    @Test
    void testGetDefinitionByName_WithNullName() {
        // Arrange
        when(serviceManager.getDefinitionsByName(null))
                .thenReturn(Collections.emptyList());

        // Act
        ResponseEntity<?> response = controller.getDefinitionByName(null);

        // Assert
        assertEquals(404, response.getStatusCode().value());
        verify(serviceManager).getDefinitionsByName(null);
    }

    @Test
    void testGetDefinitionByName_WithEmptyName() {
        // Arrange
        when(serviceManager.getDefinitionsByName(""))
                .thenReturn(Collections.emptyList());

        // Act
        ResponseEntity<?> response = controller.getDefinitionByName("");

        // Assert
        assertEquals(404, response.getStatusCode().value());
        verify(serviceManager).getDefinitionsByName("");
    }

    @Test
    void testGetDefinitionByName_WithSpecialCharacters() {
        // Arrange
        String specialName = "test-integration-with-special-chars_123";
        HIPIntegrationDefinition specialDef = createTestDefinition(specialName, "1.0");
        when(serviceManager.getDefinitionsByName(specialName))
                .thenReturn(Collections.singletonList(specialDef));

        // Act
        ResponseEntity<?> response = controller.getDefinitionByName(specialName);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertTrue(response.getBody() instanceof List);
        
        @SuppressWarnings("unchecked")
        List<HIPIntegrationDefinition> returnedDefinitions = (List<HIPIntegrationDefinition>) response.getBody();
        assertEquals(1, returnedDefinitions.size());
        assertEquals(specialName, returnedDefinitions.get(0).getHipIntegrationName());
        
        verify(serviceManager).getDefinitionsByName(specialName);
    }

    // === Helper methods ===

    private HIPIntegrationDefinition createTestDefinition(String name, String version) {
        HIPIntegrationDefinition definition = new HIPIntegrationDefinition();
        definition.setHipIntegrationName(name);
        definition.setVersion(version);
        definition.setServiceManagerName("test-service-manager");
        definition.setBusinessFlowName("test-flow");
        definition.setDescription("Test integration definition for " + name + " v" + version);
        definition.setOwner("test-team");
        return definition;
    }
}
