package com.dell.it.hip.exception;

/**
 * Exception thrown when integration lifecycle operations fail (unregister, pause, resume, shutdown).
 */
public class IntegrationOperationException extends RuntimeException {
    
    private final String integrationName;
    private final String version;
    private final String operation;
    
    public IntegrationOperationException(String message) {
        super(message);
        this.integrationName = null;
        this.version = null;
        this.operation = null;
    }
    
    public IntegrationOperationException(String message, Throwable cause) {
        super(message, cause);
        this.integrationName = null;
        this.version = null;
        this.operation = null;
    }
    
    public IntegrationOperationException(String operation, String integrationName, String version, String message) {
        super(String.format("Failed to %s integration %s:%s - %s", operation, integrationName, version, message));
        this.operation = operation;
        this.integrationName = integrationName;
        this.version = version;
    }
    
    public IntegrationOperationException(String operation, String integrationName, String version, String message, Throwable cause) {
        super(String.format("Failed to %s integration %s:%s - %s", operation, integrationName, version, message), cause);
        this.operation = operation;
        this.integrationName = integrationName;
        this.version = version;
    }
    
    public IntegrationOperationException(String operation, String integrationName, String version, Throwable cause) {
        super(String.format("Failed to %s integration %s:%s: %s", 
                          operation, integrationName, version, cause.getMessage()), cause);
        this.operation = operation;
        this.integrationName = integrationName;
        this.version = version;
    }
    
    public String getIntegrationName() {
        return integrationName;
    }
    
    public String getVersion() {
        return version;
    }
    
    public String getOperation() {
        return operation;
    }
}
