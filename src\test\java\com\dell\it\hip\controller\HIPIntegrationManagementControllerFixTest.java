package com.dell.it.hip.controller;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.IntegrationStatus;
import com.dell.it.hip.controller.dto.IntegrationDefinitionsWithStatusResponse;
import com.dell.it.hip.core.HIPIntegrationOrchestrationService;
import com.dell.it.hip.core.ServiceManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Test to verify the fix for the JUnit test failure where orchestrationService.getAllHIPIntegrationsWithStatus()
 * was being called multiple times instead of once.
 */
@ExtendWith(MockitoExtension.class)
public class HIPIntegrationManagementControllerFixTest {

    @Mock
    private HIPIntegrationOrchestrationService orchestrationService;

    @Mock
    private ServiceManager serviceManager;

    @InjectMocks
    private HIPIntegrationManagementController controller;

    private HIPIntegrationDefinition testDefinitionV1;
    private HIPIntegrationDefinition testDefinitionV2;
    private HIPIntegrationDefinition testDefinitionV3;
    
    private HIPIntegrationOrchestrationService.HIPIntegrationInfo testInfoV1;
    private HIPIntegrationOrchestrationService.HIPIntegrationInfo testInfoV2;
    private HIPIntegrationOrchestrationService.HIPIntegrationInfo testInfoV3;

    @BeforeEach
    void setUp() {
        // Create test definitions for different versions of the same integration
        testDefinitionV1 = createTestDefinition("test-integration", "1.0");
        testDefinitionV2 = createTestDefinition("test-integration", "2.0");
        testDefinitionV3 = createTestDefinition("test-integration", "3.0");
        
        // Create test integration info objects with status
        testInfoV1 = new HIPIntegrationOrchestrationService.HIPIntegrationInfo("test-integration", "1.0", IntegrationStatus.RUNNING);
        testInfoV2 = new HIPIntegrationOrchestrationService.HIPIntegrationInfo("test-integration", "2.0", IntegrationStatus.PAUSED);
        testInfoV3 = new HIPIntegrationOrchestrationService.HIPIntegrationInfo("test-integration", "3.0", IntegrationStatus.RUNNING);
    }

    @Test
    void testGetDefinitionByName_OrchestrationServiceCalledOnlyOnce() {
        // Arrange
        List<HIPIntegrationDefinition> expectedDefinitions = Arrays.asList(
                testDefinitionV1, testDefinitionV2, testDefinitionV3);
        List<HIPIntegrationOrchestrationService.HIPIntegrationInfo> expectedInfos = Arrays.asList(
                testInfoV1, testInfoV2, testInfoV3);
        
        when(serviceManager.getDefinitionsByName("test-integration"))
                .thenReturn(expectedDefinitions);
        when(orchestrationService.getAllHIPIntegrationsWithStatus())
                .thenReturn(expectedInfos);

        // Act
        ResponseEntity<IntegrationDefinitionsWithStatusResponse> response = controller.getDefinitionByName("test-integration");

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        
        IntegrationDefinitionsWithStatusResponse responseBody = response.getBody();
        
        // Verify definitions
        List<HIPIntegrationDefinition> returnedDefinitions = responseBody.getDefinitions();
        assertNotNull(returnedDefinitions);
        assertEquals(3, returnedDefinitions.size());
        
        // Verify status information
        Map<String, IntegrationStatus> statusMap = responseBody.getStatus();
        assertNotNull(statusMap);
        assertEquals(3, statusMap.size());
        assertEquals(IntegrationStatus.RUNNING, statusMap.get("1.0"));
        assertEquals(IntegrationStatus.PAUSED, statusMap.get("2.0"));
        assertEquals(IntegrationStatus.RUNNING, statusMap.get("3.0"));
        
        // CRITICAL: Verify that orchestrationService.getAllHIPIntegrationsWithStatus() is called exactly once
        // This was the root cause of the test failure - it was being called 3 times (once per definition)
        verify(serviceManager).getDefinitionsByName("test-integration");
        verify(orchestrationService, times(1)).getAllHIPIntegrationsWithStatus();
    }

    private HIPIntegrationDefinition createTestDefinition(String name, String version) {
        HIPIntegrationDefinition definition = new HIPIntegrationDefinition();
        definition.setHipIntegrationName(name);
        definition.setVersion(version);
        definition.setServiceManagerName("test-service-manager");
        definition.setBusinessFlowName("test-flow");
        definition.setDescription("Test integration definition for " + name + " v" + version);
        definition.setOwner("test-team");
        return definition;
    }
}
