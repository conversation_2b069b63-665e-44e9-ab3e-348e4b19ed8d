package com.dell.it.hip.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.Objects;

/**
 * Represents a tag with key-value pair structure for HIP Integration metadata.
 * Follows the established dot-separated lowercase naming convention for JSON properties.
 */
public class Tag implements Serializable {

    @JsonProperty("key")
    private String key;

    @JsonProperty("value")
    private String value;

    /**
     * Default constructor for JSON deserialization.
     */
    public Tag() {
    }

    /**
     * Constructor with key and value.
     *
     * @param key   the tag key
     * @param value the tag value
     */
    public Tag(String key, String value) {
        this.key = key;
        this.value = value;
    }

    /**
     * Gets the tag key.
     *
     * @return the tag key
     */
    public String getKey() {
        return key;
    }

    /**
     * Sets the tag key.
     *
     * @param key the tag key
     */
    public void setKey(String key) {
        this.key = key;
    }

    /**
     * Gets the tag value.
     *
     * @return the tag value
     */
    public String getValue() {
        return value;
    }

    /**
     * Sets the tag value.
     *
     * @param value the tag value
     */
    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Tag tag = (Tag) o;
        return Objects.equals(key, tag.key) && Objects.equals(value, tag.value);
    }

    @Override
    public int hashCode() {
        return Objects.hash(key, value);
    }

    @Override
    public String toString() {
        return "Tag{" +
                "key='" + key + '\'' +
                ", value='" + value + '\'' +
                '}';
    }
}
