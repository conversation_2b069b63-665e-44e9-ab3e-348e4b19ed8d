package com.dell.it.hip.exception;

/**
 * Exception thrown when integration registration fails.
 */
public class IntegrationRegistrationException extends RuntimeException {
    
    private final String integrationName;
    private final String version;
    
    public IntegrationRegistrationException(String message) {
        super(message);
        this.integrationName = null;
        this.version = null;
    }
    
    public IntegrationRegistrationException(String message, Throwable cause) {
        super(message, cause);
        this.integrationName = null;
        this.version = null;
    }
    
    public IntegrationRegistrationException(String integrationName, String version, String message) {
        super(String.format("Failed to register integration %s:%s - %s", integrationName, version, message));
        this.integrationName = integrationName;
        this.version = version;
    }
    
    public IntegrationRegistrationException(String integrationName, String version, String message, Throwable cause) {
        super(String.format("Failed to register integration %s:%s - %s", integrationName, version, message), cause);
        this.integrationName = integrationName;
        this.version = version;
    }
    
    public IntegrationRegistrationException(String integrationName, String version, Throwable cause) {
        super(String.format("Failed to register integration %s:%s: %s", 
                          integrationName, version, cause.getMessage()), cause);
        this.integrationName = integrationName;
        this.version = version;
    }
    
    public String getIntegrationName() {
        return integrationName;
    }
    
    public String getVersion() {
        return version;
    }
}
