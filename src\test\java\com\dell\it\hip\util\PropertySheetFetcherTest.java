package com.dell.it.hip.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

public class PropertySheetFetcherTest {

    @Test
    public void testCombineAllJsonNodeValues() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> combinedProps = new LinkedHashMap<>();

        // Sample JSON with multiple property sources (similar to the main method)
        String json = "{\"name\":\"test-sheet\",\"profiles\":[\"cloud\"],\"label\":null,\"version\":null,\"state\":null,\"propertySources\":[{\"name\":\"vault:shared-kafka-producer\",\"source\":{\"kafka.producer.sasl.jaas.config.password\":\"password1\",\"kafka.producer.sasl.jaas.config.username\":\"user1\",\"kafka.producer.ssl.truststore.password\":\"trustpass1\"}},{\"name\":\"vault:shared-kafka-consumer\",\"source\":{\"kafka.consumer.sasl.jaas.config.password\":\"password2\",\"kafka.consumer.sasl.jaas.config.username\":\"user2\",\"kafka.consumer.ssl.truststore.password\":\"trustpass2\"}},{\"name\":\"vault:shared-ibmmq-producer\",\"source\":{\"ibm.mq.producer.password\":\"mqpassword\",\"ibm.mq.producer.user\":\"mquser\"}}]}";

        JsonNode root = objectMapper.readTree(json);
        JsonNode sources = root.get("propertySources");

        if (sources != null && sources.isArray()) {
            for (JsonNode src : sources) {
                JsonNode sourceProps = src.get("source");
                if (sourceProps != null) {
                    // Iterate through all fields in this source and add them to the combined properties
                    Iterator<Map.Entry<String, JsonNode>> fields = sourceProps.fields();
                    while (fields.hasNext()) {
                        Map.Entry<String, JsonNode> entry = fields.next();
                        // Convert JsonNode to appropriate Object type for the combined map
                        Object value;
                        if (entry.getValue().isTextual()) {
                            value = entry.getValue().asText();
                        } else if (entry.getValue().isNumber()) {
                            value = entry.getValue().numberValue();
                        } else if (entry.getValue().isBoolean()) {
                            value = entry.getValue().asBoolean();
                        } else {
                            // For complex objects, keep as JsonNode or convert to string
                            value = entry.getValue().toString();
                        }
                        combinedProps.put(entry.getKey(), value);
                    }
                }
            }
        }

        // Convert the combined properties Map to a JsonNode (as the fixed implementation does)
        JsonNode combinedJsonNode = objectMapper.valueToTree(combinedProps);

        Map<String, Object> result = new LinkedHashMap<>();
        result.put("mysheet", combinedJsonNode);

        // Verify that the result contains a JsonNode (not a Map)
        Object mysheetObj = result.get("mysheet");
        assertTrue(mysheetObj instanceof JsonNode, "The value should be a JsonNode, not a Map");

        JsonNode mysheet = (JsonNode) mysheetObj;

        // Verify properties from first source (kafka-producer)
        assertEquals("password1", mysheet.get("kafka.producer.sasl.jaas.config.password").asText());
        assertEquals("user1", mysheet.get("kafka.producer.sasl.jaas.config.username").asText());
        assertEquals("trustpass1", mysheet.get("kafka.producer.ssl.truststore.password").asText());

        // Verify properties from second source (kafka-consumer)
        assertEquals("password2", mysheet.get("kafka.consumer.sasl.jaas.config.password").asText());
        assertEquals("user2", mysheet.get("kafka.consumer.sasl.jaas.config.username").asText());
        assertEquals("trustpass2", mysheet.get("kafka.consumer.ssl.truststore.password").asText());

        // Verify properties from third source (ibmmq-producer)
        assertEquals("mqpassword", mysheet.get("ibm.mq.producer.password").asText());
        assertEquals("mquser", mysheet.get("ibm.mq.producer.user").asText());

        // Verify total number of properties (should be 8 total)
        assertEquals(8, mysheet.size());

        // CRITICAL TEST: Verify that the JsonNode can be used with objectMapper.readValue()
        // This simulates how downstream code would use it
        @SuppressWarnings("unchecked")
        Map<String, Object> deserializedMap = objectMapper.readValue(mysheet.toString(), Map.class);
        assertEquals(8, deserializedMap.size());
        assertEquals("password1", deserializedMap.get("kafka.producer.sasl.jaas.config.password"));
        assertEquals("mquser", deserializedMap.get("ibm.mq.producer.user"));

        System.out.println("Combined properties as JsonNode: " + result.toString());
        System.out.println("JsonNode can be deserialized: " + deserializedMap.toString());
        System.out.println("Test passed! All JSON node values are properly combined into a JsonNode.");
    }
    
    @Test
    public void testOriginalBehaviorWouldOnlyKeepLastSource() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode sourceProps = null; // This simulates the original buggy behavior
        
        String json = "{\"name\":\"test-sheet\",\"profiles\":[\"cloud\"],\"label\":null,\"version\":null,\"state\":null,\"propertySources\":[{\"name\":\"vault:shared-kafka-producer\",\"source\":{\"kafka.producer.sasl.jaas.config.password\":\"password1\",\"kafka.producer.sasl.jaas.config.username\":\"user1\"}},{\"name\":\"vault:shared-kafka-consumer\",\"source\":{\"kafka.consumer.sasl.jaas.config.password\":\"password2\",\"kafka.consumer.sasl.jaas.config.username\":\"user2\"}},{\"name\":\"vault:shared-ibmmq-producer\",\"source\":{\"ibm.mq.producer.password\":\"mqpassword\",\"ibm.mq.producer.user\":\"mquser\"}}]}";

        JsonNode root = objectMapper.readTree(json);
        JsonNode sources = root.get("propertySources");
       
        if (sources != null && sources.isArray()) {
            for (JsonNode src : sources) {
                sourceProps = src.get("source"); // This overwrites on each iteration - the bug!
            }
        }
       
        Map<String, Object> result = new LinkedHashMap<>();
        result.put("mysheet", sourceProps);
        
        // With the original buggy behavior, only the last source would be present
        JsonNode mysheet = (JsonNode) result.get("mysheet");
        
        // Only properties from the last source (ibmmq-producer) would be present
        assertTrue(mysheet.has("ibm.mq.producer.password"));
        assertTrue(mysheet.has("ibm.mq.producer.user"));
        
        // Properties from earlier sources would be missing
        assertFalse(mysheet.has("kafka.producer.sasl.jaas.config.password"));
        assertFalse(mysheet.has("kafka.consumer.sasl.jaas.config.password"));
        
        System.out.println("Original buggy behavior result: " + result.toString());
        System.out.println("This demonstrates the original issue - only last source is kept!");
    }

    // Static class for testing deserialization
    public static class KafkaConfig {
        public String username;
        public String password;
        public String protocol;
        public String mechanism;

        // Default constructor for Jackson
        public KafkaConfig() {}
    }

    @Test
    public void testJsonNodeCanBeUsedForDeserialization() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();

        // Sample JSON with kafka producer properties
        String json = "{\"name\":\"test-sheet\",\"profiles\":[\"cloud\"],\"label\":null,\"version\":null,\"state\":null,\"propertySources\":[{\"name\":\"vault:shared-kafka-producer\",\"source\":{\"kafka.producer.sasl.jaas.config.username\":\"testuser\",\"kafka.producer.sasl.jaas.config.password\":\"testpass\",\"kafka.producer.security.protocol\":\"SASL_SSL\",\"kafka.producer.sasl.mechanism\":\"PLAIN\"}}]}";

        JsonNode root = objectMapper.readTree(json);
        JsonNode sources = root.get("propertySources");

        // Combine all properties into a Map, then convert to JsonNode
        Map<String, Object> combinedProps = new LinkedHashMap<>();
        if (sources != null && sources.isArray()) {
            for (JsonNode src : sources) {
                JsonNode sourceProps = src.get("source");
                if (sourceProps != null) {
                    Iterator<Map.Entry<String, JsonNode>> fields = sourceProps.fields();
                    while (fields.hasNext()) {
                        Map.Entry<String, JsonNode> entry = fields.next();
                        Object value = entry.getValue().isTextual() ? entry.getValue().asText() : entry.getValue().toString();
                        combinedProps.put(entry.getKey(), value);
                    }
                }
            }
        }

        // Convert to JsonNode (as the fixed implementation does)
        JsonNode combinedJsonNode = objectMapper.valueToTree(combinedProps);

        Map<String, Object> result = new LinkedHashMap<>();
        result.put("mysheet", combinedJsonNode);

        // Get the JsonNode from the result (simulating downstream code)
        JsonNode configJsonNode = (JsonNode) result.get("mysheet");

        // Create a subset JsonNode for kafka producer config (simulating property extraction)
        Map<String, Object> kafkaProducerProps = new LinkedHashMap<>();
        kafkaProducerProps.put("username", configJsonNode.get("kafka.producer.sasl.jaas.config.username").asText());
        kafkaProducerProps.put("password", configJsonNode.get("kafka.producer.sasl.jaas.config.password").asText());
        kafkaProducerProps.put("protocol", configJsonNode.get("kafka.producer.security.protocol").asText());
        kafkaProducerProps.put("mechanism", configJsonNode.get("kafka.producer.sasl.mechanism").asText());

        JsonNode kafkaConfigNode = objectMapper.valueToTree(kafkaProducerProps);

        // CRITICAL TEST: Use objectMapper.readValue() to deserialize into a configuration class
        // This is exactly how downstream code would use the JsonNode
        KafkaConfig kafkaConfig = objectMapper.readValue(kafkaConfigNode.toString(), KafkaConfig.class);

        // Verify the deserialization worked correctly
        assertEquals("testuser", kafkaConfig.username);
        assertEquals("testpass", kafkaConfig.password);
        assertEquals("SASL_SSL", kafkaConfig.protocol);
        assertEquals("PLAIN", kafkaConfig.mechanism);

        System.out.println("JsonNode successfully deserialized into KafkaConfig:");
        System.out.println("  username: " + kafkaConfig.username);
        System.out.println("  password: " + kafkaConfig.password);
        System.out.println("  protocol: " + kafkaConfig.protocol);
        System.out.println("  mechanism: " + kafkaConfig.mechanism);
        System.out.println("Test passed! JsonNode can be used for configuration class deserialization.");
    }
}
